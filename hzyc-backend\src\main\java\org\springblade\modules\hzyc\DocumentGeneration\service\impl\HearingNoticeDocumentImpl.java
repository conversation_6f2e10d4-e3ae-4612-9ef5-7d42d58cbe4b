package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 听证告知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("hearingNoticeDocument")
public class HearingNoticeDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);

        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "30案件听证告知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "HEARING-NOTICE";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        // 听证告知书字段映射 - 基于接口字段，使用下划线分割
        mapping.put("SJBM", "city_org_code");
        mapping.put("WSH", "document_serial");
        mapping.put("WFTK", "violated_law");
        mapping.put("TZGZSBS", "hearing_notice_id");
        mapping.put("CJR", "creator");
        mapping.put("XGR", "modifier");
        mapping.put("SJSSBMYYTYSJQXCL", "dept_data_permission");
        mapping.put("YCZMJYB", "bureau_postal_code");
        mapping.put("FWZXZDTBSJYFWZXSJS", "service_center_sync_delete");
        mapping.put("LASJ", "case_filing_date");
        mapping.put("SAR", "involved_person");
        mapping.put("KZZD3", "ext3");
        mapping.put("KZZD2", "ext2");
        mapping.put("MCRKSJ", "mc_tec_ctime");
        mapping.put("CJSJ", "create_time");
        mapping.put("CFYJ", "legal_basis");
        mapping.put("XTGXSJCXBYDX", "sys_update_time");
        mapping.put("XZCF", "administrative_penalty");
        mapping.put("SFYX", "is_active");
        mapping.put("WSHQ", "full_doc_no");
        mapping.put("ND", "document_year");
        mapping.put("DWJC", "bureau_name");
        mapping.put("BZ", "remark");
        mapping.put("ZJLJ", "evidence_list");
        mapping.put("DSRYJ", "party_opinion");
        mapping.put("XYWYBS", "industry_unique_id");
        mapping.put("XTCJSJCXBYDX", "sys_create_time");
        mapping.put("YCZMJDZ", "bureau_address");
        mapping.put("AJXZ", "case_nature");
        mapping.put("AY", "case_cause");
        mapping.put("KZZD1", "ext1");
        mapping.put("SJSSDWYYTYSJQXCL", "unit_data_permission");
        mapping.put("ZFZH", "enforcement_cert_no");
        mapping.put("XGSJ", "modify_time");
        mapping.put("YCZMJLXDH", "bureau_contact_phone");
        mapping.put("SFLX0F1S", "is_type_flag");
        mapping.put("WFSS", "violation_facts");
        mapping.put("AJBS", "case_id");
        mapping.put("DWSXZ", "unit_abbreviation");
        mapping.put("SJMC", "city_org_name");
        mapping.put("WFYZCD", "violation_severity");
        mapping.put("FWZXZDTBSJYFWZXSJG", "service_center_sync_update");
        mapping.put("CBRUUIDS", "undertaker_uuids");
        mapping.put("CBR", "undertaker");
        mapping.put("WSRQ", "doc_date");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {
        Map<String, Object> mockData = new HashMap<>();

        if (type == 1) {
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getHearingNoticeDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if (array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if (firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });

                    // 处理特殊字段
                    processSpecialFields(processData);
                    return processData;
                }
            }
        }

        // 基础信息 - 使用下划线分割的字段名
        mockData.put("case_id", "6358d676d24647f1b824c1f362674299");
        mockData.put("hearing_notice_id", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("bureau_name", "惠阳区");
        mockData.put("document_prefix", "惠阳烟听告");
        mockData.put("document_year", "2025");
        mockData.put("document_serial", "5");
        mockData.put("full_doc_no", "惠阳烟听告﹝2025﹞第5号");

        // 当事人信息
        mockData.put("involved_person", "梁俊强");

        // 违法事实
        mockData.put("violation_year", "2025");
        mockData.put("violation_month", "03");
        mockData.put("violation_day", "18");
        mockData.put("case_filing_date", "2025年03月18日");
        mockData.put("case_nature", "未在当地烟草专卖批发企业进货");
        mockData.put("case_cause", "未在当地烟草专卖批发企业进货");
        mockData.put("violation_facts", "2025年03月18日，我局接群众举报称位于广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号的\"博罗县龙溪隆胜轩茶烟酒商行\"有涉嫌违法经营卷烟的行为");

        // 法律依据
        mockData.put("violated_law", "《中华人民共和国烟草专卖法实施条例》第二十三条第二款");
        mockData.put("legal_basis", "《中华人民共和国烟草专卖法实施条例》第五十六条");

        // 拟处罚决定
        mockData.put("administrative_penalty", "处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元");

        // 联系信息
        mockData.put("bureau_address", "广东省惠州市惠阳区淡水街道人民四路88号");
        mockData.put("bureau_postal_code", "516211");
        mockData.put("bureau_contact_phone", "0752-3808888");

        // 证据信息
        mockData.put("evidence_list", "1. 现场检查笔录1份；2. 询问笔录1份；3. 证据先行登记保存通知书1份；4. 卷烟、雪茄烟鉴别检验报告1份");

        // 当事人意见
        mockData.put("party_opinion", "");

        // 签名信息
        mockData.put("official_seal", "广东省惠州市惠阳区烟草专卖局");
        mockData.put("sign_year", "2025");
        mockData.put("sign_month", "06");
        mockData.put("sign_day", "15");
        mockData.put("doc_date", "2025年06月15日");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/15 10:30");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/15 10:30");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("unit_abbreviation", "惠阳区");
        mockData.put("mc_tec_ctime", "2025/6/15 10:30");
        mockData.put("sys_create_time", "2025/6/15 10:30");
        mockData.put("sys_update_time", "2025/6/15 10:30");
        mockData.put("industry_unique_id", "");
        mockData.put("remark", "");
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");

        // 权限相关字段
        mockData.put("dept_data_permission", "4413231030000002829");
        mockData.put("unit_data_permission", "4413231030000000540");
        mockData.put("service_center_sync_delete", 0);
        mockData.put("service_center_sync_update", "");

        // 执法相关字段
        mockData.put("undertaker", "叶辉明,朱兆强");
        mockData.put("undertaker_uuids", "4413231223000010669,4413231223000010670");
        mockData.put("enforcement_cert_no", "19090352015,19090352023");
        mockData.put("is_type_flag", 1);
        mockData.put("violation_severity", "一般");

        return mockData;
    }

    /**
     * 处理特殊字段，如日期格式转换等
     */
    private void processSpecialFields(Map<String, Object> data) {
        // 处理立案日期，从时间戳转换为年月日
        if (data.containsKey("case_filing_date") && data.get("case_filing_date") instanceof Long) {
            Long timestamp = (Long) data.get("case_filing_date");
            java.util.Date date = new java.util.Date(timestamp);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日");
            String formattedDate = sdf.format(date);
            data.put("case_filing_date", formattedDate);

            // 同时设置年月日字段
            java.text.SimpleDateFormat yearFormat = new java.text.SimpleDateFormat("yyyy");
            java.text.SimpleDateFormat monthFormat = new java.text.SimpleDateFormat("MM");
            java.text.SimpleDateFormat dayFormat = new java.text.SimpleDateFormat("dd");
            data.put("violation_year", yearFormat.format(date));
            data.put("violation_month", monthFormat.format(date));
            data.put("violation_day", dayFormat.format(date));
        }

        // 处理文档日期
        if (data.containsKey("doc_date") && data.get("doc_date") instanceof Long) {
            Long timestamp = (Long) data.get("doc_date");
            java.util.Date date = new java.util.Date(timestamp);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日");
            String formattedDate = sdf.format(date);
            data.put("doc_date", formattedDate);

            // 同时设置签名日期字段
            java.text.SimpleDateFormat yearFormat = new java.text.SimpleDateFormat("yyyy");
            java.text.SimpleDateFormat monthFormat = new java.text.SimpleDateFormat("MM");
            java.text.SimpleDateFormat dayFormat = new java.text.SimpleDateFormat("dd");
            data.put("sign_year", yearFormat.format(date));
            data.put("sign_month", monthFormat.format(date));
            data.put("sign_day", dayFormat.format(date));
        }

        // 处理文档编号，提取前缀和年份
        if (data.containsKey("full_doc_no") && data.get("full_doc_no") != null) {
            String fullDocNo = data.get("full_doc_no").toString();
            // 例如：龙门烟处告﹝2023﹞第59号
            if (fullDocNo.contains("﹝") && fullDocNo.contains("﹞")) {
                String prefix = fullDocNo.substring(0, fullDocNo.indexOf("﹝"));
                String yearPart = fullDocNo.substring(fullDocNo.indexOf("﹝") + 1, fullDocNo.indexOf("﹞"));
                String numberPart = fullDocNo.substring(fullDocNo.indexOf("第") + 1, fullDocNo.indexOf("号"));

                data.put("document_prefix", prefix);
                data.put("document_year", yearPart);
                data.put("document_serial", numberPart);
            }
        }

        // 处理承办人信息
        if (data.containsKey("undertaker_uuids") && data.get("undertaker_uuids") != null) {
            String uuids = data.get("undertaker_uuids").toString();
            if (uuids.contains(",")) {
                String[] uuidArray = uuids.split(",");
                data.put("undertaker_count", uuidArray.length);
            }
        }
    }
}
