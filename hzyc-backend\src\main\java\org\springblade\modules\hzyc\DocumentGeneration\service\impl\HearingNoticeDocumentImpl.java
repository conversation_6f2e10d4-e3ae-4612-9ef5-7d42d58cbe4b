package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 听证告知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("hearingNoticeDocument")
public class HearingNoticeDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "听证告知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "HEARING-NOTICE";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        // 听证告知书字段映射 - 与前端Vue组件字段名保持一致
        mapping.put("AJBH", "case_code");
        mapping.put("AJMC", "case_name");
        mapping.put("DWJC", "bureauName");
        mapping.put("WSHQ", "documentNumber");
        mapping.put("WSH", "document_serial");
        mapping.put("ND", "documentYear");
        mapping.put("DSR", "recipient");
        mapping.put("AFSJ", "violation_date");
        mapping.put("AJXZ", "violationReason");
        mapping.put("WFTK", "violatedLaw");
        mapping.put("CFYJ", "legalBasis");
        mapping.put("CFXX", "proposedPenalty");
        mapping.put("DWDZ", "bureauAddress");
        mapping.put("YB", "postalCode");
        mapping.put("ZFBMDH", "contactPhone");
        mapping.put("WSRQ", "doc_date");
        mapping.put("CJR", "creator");
        mapping.put("CJSJ", "create_time");
        mapping.put("XGR", "modifier");
        mapping.put("XGSJ", "modify_time");
        mapping.put("SJBM", "city_org_code");
        mapping.put("SJMC", "city_org_name");
        mapping.put("ZZJGUUID", "org_uuid");
        mapping.put("CBBMUUID", "reg_dept_uuid");
        mapping.put("SFYX", "is_active");
        mapping.put("ZLBS", "dc_tec_operation");
        mapping.put("RKSJ", "dc_tec_ctime");
        mapping.put("GXSJ", "dc_tec_utime");
        mapping.put("MCRKSJ", "mc_tec_ctime");
        mapping.put("XYWYBS", "tid");
        mapping.put("BZ", "remark");
        mapping.put("KZZD1", "ext1");
        mapping.put("KZZD2", "ext2");
        mapping.put("KZZD3", "ext3");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {
        Map<String, Object> mockData = new HashMap<>();
        
        if (type == 1) {
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getAdminPenaltyNoticeDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if (array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if (firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });

                    // 处理特殊字段
                    processSpecialFields(processData);
                    return processData;
                }
            }
        }

        // 基础信息 - 与前端Vue组件字段名保持一致
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("bureauName", "惠阳区");
        mockData.put("documentPrefix", "惠阳烟听告");
        mockData.put("documentYear", "2025");
        mockData.put("documentNumber", "5");
        mockData.put("document_serial", "5");
        mockData.put("full_doc_no", "惠阳烟听告﹝2025﹞第5号");

        // 当事人信息
        mockData.put("recipient", "梁俊强");

        // 违法事实
        mockData.put("violationYear", "2025");
        mockData.put("violationMonth", "03");
        mockData.put("violationDay", "18");
        mockData.put("violation_date", "2025年03月18日");
        mockData.put("violationReason", "未在当地烟草专卖批发企业进货");

        // 法律依据
        mockData.put("violatedLaw", "《中华人民共和国烟草专卖法实施条例》第二十三条第二款");
        mockData.put("legalBasis", "《中华人民共和国烟草专卖法实施条例》第五十六条");

        // 拟处罚决定
        mockData.put("proposedPenalty", "处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元");

        // 联系信息
        mockData.put("bureauAddress", "广东省惠州市惠阳区淡水街道人民四路88号");
        mockData.put("postalCode", "516211");
        mockData.put("contactPhone", "0752-3808888");

        // 签名信息
        mockData.put("officialSeal", "广东省惠州市惠阳区烟草专卖局");
        mockData.put("signYear", "2025");
        mockData.put("signMonth", "06");
        mockData.put("signDay", "15");
        mockData.put("doc_date", "2025年06月15日");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/15 10:30");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/15 10:30");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("org_uuid", "4413231030000000540");
        mockData.put("reg_dept_uuid", "4413231030000002829");
        mockData.put("dc_tec_operation", "I");
        mockData.put("dc_tec_ctime", "2025/6/15 10:30");
        mockData.put("dc_tec_utime", "2025/6/15 10:30");
        mockData.put("mc_tec_ctime", "2025/6/15 10:30");
        mockData.put("tid", "");
        mockData.put("remark", "");
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");

        return mockData;
    }

    /**
     * 处理特殊字段，如日期格式转换等
     */
    private void processSpecialFields(Map<String, Object> data) {
        // 处理违法日期，从时间戳转换为年月日
        if (data.containsKey("violation_date") && data.get("violation_date") instanceof Long) {
            Long timestamp = (Long) data.get("violation_date");
            java.util.Date date = new java.util.Date(timestamp);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日");
            String formattedDate = sdf.format(date);
            data.put("violation_date", formattedDate);

            // 同时设置年月日字段
            java.text.SimpleDateFormat yearFormat = new java.text.SimpleDateFormat("yyyy");
            java.text.SimpleDateFormat monthFormat = new java.text.SimpleDateFormat("MM");
            java.text.SimpleDateFormat dayFormat = new java.text.SimpleDateFormat("dd");
            data.put("violationYear", yearFormat.format(date));
            data.put("violationMonth", monthFormat.format(date));
            data.put("violationDay", dayFormat.format(date));
        }

        // 处理文档日期
        if (data.containsKey("doc_date") && data.get("doc_date") instanceof Long) {
            Long timestamp = (Long) data.get("doc_date");
            java.util.Date date = new java.util.Date(timestamp);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日");
            String formattedDate = sdf.format(date);
            data.put("doc_date", formattedDate);

            // 同时设置签名日期字段
            java.text.SimpleDateFormat yearFormat = new java.text.SimpleDateFormat("yyyy");
            java.text.SimpleDateFormat monthFormat = new java.text.SimpleDateFormat("MM");
            java.text.SimpleDateFormat dayFormat = new java.text.SimpleDateFormat("dd");
            data.put("signYear", yearFormat.format(date));
            data.put("signMonth", monthFormat.format(date));
            data.put("signDay", dayFormat.format(date));
        }

        // 处理文档编号，提取前缀和年份
        if (data.containsKey("documentNumber") && data.get("documentNumber") != null) {
            String fullDocNo = data.get("documentNumber").toString();
            // 例如：龙门烟处告﹝2023﹞第59号
            if (fullDocNo.contains("﹝") && fullDocNo.contains("﹞")) {
                String prefix = fullDocNo.substring(0, fullDocNo.indexOf("﹝"));
                String yearPart = fullDocNo.substring(fullDocNo.indexOf("﹝") + 1, fullDocNo.indexOf("﹞"));
                String numberPart = fullDocNo.substring(fullDocNo.indexOf("第") + 1, fullDocNo.indexOf("号"));

                data.put("documentPrefix", prefix);
                data.put("documentYear", yearPart);
                data.put("documentNumber", numberPart);
            }
        }
    }
}
