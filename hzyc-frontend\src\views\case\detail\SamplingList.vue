<template>
  <div class="sampling-list-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>抽样取证物品清单</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_short_name"
                placeholder="机构名称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>抽样取证物品清单</h2>
            </div>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.legalBasis"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="根据《中华人民共和国行政处罚法》第五十六条的规定，现对下列物品予以抽样取证："
                class="legal-basis auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 抽样信息表格 -->
          <div class="sampling-table-container">
            <table class="sampling-table">
              <tbody>
                <!-- 当事人和立案编号 -->
                <tr>
                  <td class="label-cell">当事人</td>
                  <td class="content-cell">
                    <el-input v-model="formData.party" size="small" placeholder="当事人" />
                  </td>
                  <td class="label-cell">立案编号</td>
                  <td class="content-cell">
                    <el-input v-model="formData.case_code" size="small" placeholder="立案编号" />
                  </td>
                </tr>

                <!-- 抽样方法和抽样目的 -->
                <tr>
                  <td class="label-cell">抽样方法</td>
                  <td class="content-cell">
                    <el-input v-model="formData.samplingMethod" size="small" placeholder="抽样方法" />
                  </td>
                  <td class="label-cell">抽样目的</td>
                  <td class="content-cell">
                    <el-input v-model="formData.sample_dest" size="small" placeholder="抽样目的" />
                  </td>
                </tr>

                <!-- 抽样时间 -->
                <tr>
                  <td class="label-cell">抽样时间</td>
                  <td class="content-cell time-cell" colspan="3">
                    <div style="display: flex; align-items: center; gap: 5px; flex-wrap: wrap;">
                      <el-input v-model="formData.samplingYear" size="small" style="width: 80px;" placeholder="年" />
                      <span>年</span>
                      <el-input v-model="formData.samplingMonth" size="small" style="width: 60px;" placeholder="月" />
                      <span>月</span>
                      <el-input v-model="formData.samplingDay" size="small" style="width: 60px;" placeholder="日" />
                      <span>日</span>
                      <el-input v-model="formData.samplingHour" size="small" style="width: 60px;" placeholder="时" />
                      <span>时</span>
                      <el-input v-model="formData.samplingMinute" size="small" style="width: 60px;" placeholder="分" />
                      <span>分至</span>
                      <el-input v-model="formData.samplingEndHour" size="small" style="width: 60px;" placeholder="时" />
                      <span>时</span>
                      <el-input v-model="formData.samplingEndMinute" size="small" style="width: 60px;" placeholder="分" />
                      <span>分</span>
                    </div>
                  </td>
                </tr>

                <!-- 抽样地点 -->
                <tr>
                  <td class="label-cell">抽样地点</td>
                  <td class="content-cell" colspan="3">
                    <el-input v-model="formData.address" size="small" placeholder="抽样地点" />
                  </td>
                </tr>

                <!-- 样品表格头部 -->
                <tr class="header-row">
                  <td class="header-cell">品种规格</td>
                  <td class="header-cell">包装形式</td>
                  <td class="header-cell">样品基数（单位：）</td>
                  <td class="header-cell">样品数量（单位：）</td>
                  <td class="header-cell">样品情况</td>
                </tr>

                <!-- 样品数据行 -->
                <tr v-for="(sample, index) in formData.sampleList" :key="index">
                  <td class="data-cell">
                    <el-input v-model="sample.goods_name" size="small" placeholder="品种规格" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="sample.pack_style" size="small" placeholder="包装形式" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="sample.qty" size="small" placeholder="样品基数" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="sample.handle_qty" size="small" placeholder="样品数量" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="sample.sample_content" size="small" placeholder="样品情况" />
                  </td>
                </tr>

                <!-- 添加/删除样品按钮行 -->
                <tr>
                  <td colspan="5" style="text-align: center; padding: 10px;">
                    <el-button type="primary" size="small" @click="addSampleRow">添加样品</el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeSampleRow(formData.sampleList.length - 1)"
                      :disabled="formData.sampleList.length <= 1"
                      style="margin-left: 10px;">
                      删除最后一行
                    </el-button>
                  </td>
                </tr>

                <!-- 备注行 -->
                <tr class="remark-row">
                  <td class="label-cell">备注</td>
                  <td class="remark-content" colspan="4">
                    <el-input
                      v-model="formData.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注信息"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <!-- 承办人签名 -->
            <div class="signature-row">
              <span class="signature-label">承办人（签名）：</span>
              <el-input v-model="formData.officer1" size="small" style="width: 120px;" placeholder="承办人" />
              <span class="signature-label" style="margin-left: 30px;">执法证号：</span>
              <el-input v-model="formData.officer1License" size="small" style="width: 120px;" placeholder="执法证号" />
              <span class="date-text">
                <el-input v-model="formData.officer1Date" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <div class="signature-row">
              <span class="signature-label" style="margin-left: 120px;"></span>
              <el-input v-model="formData.officer2" size="small" style="width: 120px;" placeholder="承办人2" />
              <span class="signature-label" style="margin-left: 30px;">执法证号：</span>
              <el-input v-model="formData.officer2License" size="small" style="width: 120px;" placeholder="执法证号" />
              <span class="date-text">
                <el-input v-model="formData.officer2Date" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <!-- 当事人签名 -->
            <div class="signature-row party-signature">
              <span class="signature-label" style="color: red;">当事人（签名）：</span>
              <el-input v-model="formData.partySignature" size="small" style="width: 200px;" placeholder="当事人签名" />
              <span class="date-text" style="color: red;">
                <el-input v-model="formData.partyDate" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <!-- 见证人签名 -->
            <div class="signature-row witness-signature">
              <span class="signature-label">见证人（签名）：</span>
              <el-input v-model="formData.witnessSignature" size="small" style="width: 200px;" placeholder="见证人签名" />
              <span class="date-text">
                <el-input v-model="formData.witnessDate" size="small" style="width: 100px;" placeholder="年月日" />
              </span>
            </div>

            <!-- 送达方式 -->
            <div class="delivery-section">
              <div class="delivery-row">
                <span class="delivery-label" style="color: red;">送达方式：</span>
                <el-input v-model="formData.deliveryMethod" size="small" style="width: 150px;" placeholder="送达方式" />
                <span class="delivery-label" style="margin-left: 30px; color: red;">送达地点：</span>
                <el-input v-model="formData.deliveryLocation" size="small" style="width: 200px;" placeholder="送达地点" />
              </div>
            </div>

            <!-- 落款 -->
            <div class="footer-section">
              <div class="stamp-area">
                <span>落款（印章）</span>
              </div>
              <div class="date-area">
                <el-input v-model="formData.documentDate" size="small" style="width: 150px;" placeholder="年月日" />
              </div>
            </div>
          </div>
        </div>
      </template>



    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close, Plus, Minus } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  },
  caseNumber: {
    type: String,
    default: ''
  }
})



const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  org_short_name: '广东省博罗县烟草专卖局',
  case_code: '',
  case_name: '',
  party: '',
  address: '',
  legalBasis: '根据《中华人民共和国行政处罚法》第五十六条的规定，现对下列物品予以抽样取证：',
  sample_dest: '鉴别检验',
  samplingMethod: '',
  samplingYear: '',
  samplingMonth: '',
  samplingDay: '',
  samplingHour: '',
  samplingMinute: '',
  samplingEndHour: '',
  samplingEndMinute: '',
  samplingLocation: '',
  sampleList: [
    {
      goods_name: '',
      spec: '',
      pack_style: '',
      qty: '',
      handle_qty: '',
      unit: '',
      sample_content: '',
      remark: ''
    }
  ],
  remark: '',
  undertaker: '',
  undertaker_insp_no: '',
  // 签名相关字段
  officer1: '',
  officer1License: '',
  officer1Date: '',
  officer2: '',
  officer2License: '',
  officer2Date: '',
  partySignature: '',
  partyDate: '',
  witnessSignature: '',
  witnessDate: '',
  deliveryMethod: '',
  deliveryLocation: '',
  documentDate: '',
  // 保留的字段
  officerInfo: '',
  signatureInfo: '',
  deliveryInfo: '',
  sys_modify_time: '',
  sample_date: null,
  doc_date: null
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 数据映射函数：将后端数据映射到前端表单
const mapBackendDataToForm = (backendData) => {
  // 处理样品列表数据
  const sampleList = []

  // 检查是否有样品数据
  if (backendData.goods_name || backendData.spec || backendData.pack_style) {
    // 单个样品数据（从documentContent中）
    sampleList.push({
      goods_name: backendData.goods_name || '',
      spec: backendData.spec || '',
      pack_style: backendData.pack_style || '',
      qty: backendData.qty || '',
      handle_qty: backendData.handle_qty || '',
      unit: backendData.unit || '',
      sample_content: backendData.sample_content || '',
      remark: backendData.remark || ''
    })
  } else if (Array.isArray(backendData.sampleList)) {
    // 多个样品数据
    sampleList.push(...backendData.sampleList)
  }

  // 如果没有样品数据，添加一个空行
  if (sampleList.length === 0) {
    sampleList.push({
      goods_name: '',
      spec: '',
      pack_style: '',
      qty: '',
      handle_qty: '',
      unit: '',
      sample_content: '',
      remark: ''
    })
  }

  // 处理时间数组转换为字符串
  let samplingYear = '', samplingMonth = '', samplingDay = ''
  if (backendData.sample_date) {
    const sampleDate = new Date(backendData.sample_date)
    samplingYear = sampleDate.getFullYear().toString()
    samplingMonth = (sampleDate.getMonth() + 1).toString()
    samplingDay = sampleDate.getDate().toString()
  }

  return {
    org_short_name: backendData.org_short_name || formData.value.org_short_name,
    case_code: backendData.case_code || formData.value.case_code,
    case_name: backendData.case_name || formData.value.case_name,
    party: backendData.party || formData.value.party,
    address: backendData.address || formData.value.address,
    sample_dest: backendData.sample_dest || formData.value.sample_dest,
    sampleList: sampleList,
    remark: backendData.remark || formData.value.remark,
    undertaker: backendData.undertaker || formData.value.undertaker,
    undertaker_insp_no: backendData.undertaker_insp_no || formData.value.undertaker_insp_no,
    sample_date: backendData.sample_date || formData.value.sample_date,
    doc_date: backendData.doc_date || formData.value.doc_date,
    sys_modify_time: backendData.sys_modify_time || formData.value.sys_modify_time,
    // 保留编辑器特有字段
    legalBasis: formData.value.legalBasis,
    officerInfo: formData.value.officerInfo,
    signatureInfo: formData.value.signatureInfo,
    deliveryInfo: formData.value.deliveryInfo,
    samplingYear: samplingYear || formData.value.samplingYear,
    samplingMonth: samplingMonth || formData.value.samplingMonth,
    samplingDay: samplingDay || formData.value.samplingDay,
    samplingHour: formData.value.samplingHour,
    samplingMinute: formData.value.samplingMinute,
    samplingEndHour: formData.value.samplingEndHour,
    samplingEndMinute: formData.value.samplingEndMinute,
    samplingLocation: formData.value.samplingLocation
  }
}

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 使用映射函数处理数据，优先使用documentContent
    const dataToMap = Object.keys(docContent).length > 0 ? docContent : newVal
    const mappedData = mapBackendDataToForm(dataToMap)

    // 合并数据到表单
    formData.value = { ...formData.value, ...mappedData }

    // 如果外层有caseNumber，也要映射进去
    if (newVal.caseNumber && !formData.value.case_code) {
      formData.value.case_code = newVal.caseNumber
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 将日期组件转换为时间戳
  let sampleDate = null
  if (formData.value.samplingYear && formData.value.samplingMonth && formData.value.samplingDay) {
    const year = parseInt(formData.value.samplingYear)
    const month = parseInt(formData.value.samplingMonth) - 1 // JavaScript月份从0开始
    const day = parseInt(formData.value.samplingDay)
    const hour = parseInt(formData.value.samplingHour) || 0
    const minute = parseInt(formData.value.samplingMinute) || 0
    sampleDate = new Date(year, month, day, hour, minute).getTime()
  }

  // 保存时只传递需要的数据字段，匹配后端数据结构
  const saveData = {
    org_short_name: formData.value.org_short_name,
    case_code: formData.value.case_code,
    case_name: formData.value.case_name,
    party: formData.value.party,
    address: formData.value.address,
    sample_dest: formData.value.sample_dest,
    sample_date: sampleDate,
    doc_date: sampleDate, // 通常文档日期与抽样日期相同
    sampleList: formData.value.sampleList,
    remark: formData.value.remark,
    undertaker: formData.value.undertaker,
    undertaker_insp_no: formData.value.undertaker_insp_no,
    sys_modify_time: formData.value.sys_modify_time || new Date().getTime(),
    // 保留编辑器特有字段
    legalBasis: formData.value.legalBasis,
    officerInfo: formData.value.officerInfo,
    signatureInfo: formData.value.signatureInfo,
    deliveryInfo: formData.value.deliveryInfo
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 新增样品行
const addSampleRow = () => {
  formData.value.sampleList.push({
    goods_name: '',
    spec: '',
    pack_style: '',
    qty: '',
    handle_qty: '',
    unit: '',
    sample_content: '',
    remark: ''
  })
}

// 删除样品行
const removeSampleRow = (index) => {
  if (formData.value.sampleList.length > 1) {
    formData.value.sampleList.splice(index, 1)
  }
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '抽样取证物品清单'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'sampleList' || action === 'officerInfo') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 抽样清单特有样式 */
.sampling-list-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sample-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sample-list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.sample-table-container {
  margin-bottom: 30px;
  overflow-x: auto;
}

.sample-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
  font-size: 14px;
}

.sample-table th,
.sample-table td {
  border: 1px solid #333;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

.sample-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.sample-table td {
  min-height: 40px;
}

.sample-table .el-input {
  width: 100%;
}

.sample-table .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
}

.sample-table .el-input__inner {
  text-align: center;
}

/* 抽样信息表格样式 */
.sampling-table-container {
  margin: 30px 0;
}

.sampling-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
}

.sampling-table td {
  border: 1px solid #333;
  padding: 8px;
  vertical-align: middle;
  min-height: 40px;
}

.label-cell {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
  width: 100px;
}

.content-cell {
  padding: 8px 12px;
  text-align: left;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}

.header-row {
  background-color: #f0f0f0;
}

.header-cell {
  background-color: #f0f0f0;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
}

.data-cell {
  text-align: center;
  padding: 8px;
}

.remark-row {
  height: 100px;
}

.remark-content {
  text-align: left;
  vertical-align: top;
  padding: 10px;
}

.signature-section {
  margin-top: 50px;
}

.signature-row {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  line-height: 2;
}

.signature-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

.date-text {
  margin-left: 20px;
  color: #666;
}

.party-signature .signature-label,
.party-signature .date-text {
  color: red;
}

.delivery-section {
  margin: 30px 0;
}

.delivery-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.delivery-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

.footer-section {
  margin-top: 50px;
  text-align: right;
}

.stamp-area {
  margin-bottom: 20px;
  font-size: 16px;
}

.date-area {
  font-size: 16px;
  color: #666;
}

/* 确保输入框在表格中的样式 */
.sampling-table .el-input {
  width: 100%;
}

.sampling-table .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
}

.sampling-table .el-input__inner {
  text-align: center;
}

/* 打印样式 */
@media print {
  .document-header {
    display: none;
  }

  .sample-list-header .el-button {
    display: none;
  }

  .sample-table th:last-child,
  .sample-table td:last-child {
    display: none;
  }

  .document-layout {
    padding: 0;
    box-shadow: none;
  }
}
</style>
