<template>
  <div class="hearing-notice-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>听证告知书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.bureau_name"
                placeholder="烟草专卖局名称"
                class="org-input"
              />
              <span>烟草专卖局</span>
            </div>

            <div class="document-title">
              <h2>听证告知书</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.document_prefix"
                placeholder="文号前缀"
                style="width: 120px;"
              />
              <span>（</span>
              <el-input
                v-model="formData.document_year"
                placeholder="年份"
                style="width: 80px;"
              />
              <span>）第</span>
              <el-input
                v-model="formData.document_serial"
                placeholder="编号"
                style="width: 80px;"
              />
              <span>号</span>
            </div>
          </div>

          <!-- 收件人 -->
          <div class="content-section">
            <div class="recipient-section">
              <el-input
                v-model="formData.involved_person"
                placeholder="收件人姓名"
                style="width: 200px;"
              />
              <span>：</span>
            </div>
          </div>

          <!-- 违法事实描述 -->
          <div class="content-section">
            <div class="violation-info">
              <span>经查，你（单位）于</span>
              <el-input
                v-model="formData.violation_year"
                placeholder="年"
                style="width: 80px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.violation_month"
                placeholder="月"
                style="width: 60px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.violation_day"
                placeholder="日"
                style="width: 60px;"
              />
              <span>日因</span>
              <el-input
                v-model="formData.case_nature"
                placeholder="违法行为"
                style="width: 300px;"
              />
              <span>的行为，</span>
            </div>
          </div>

          <!-- 违法条款 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.violated_law"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="违反的法律条款"
                class="violated-law auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.legal_basis"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="法律依据"
                class="legal-basis auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 拟处罚决定 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.administrative_penalty"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="拟作出的行政处罚决定"
                class="administrative-penalty auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 听证权利告知 -->
          <div class="content-section">
            <div class="hearing-rights-notice">
              <p>根据《中华人民共和国行政处罚法》第六十三条、第六十四条和《烟草专卖行政处罚程序规定》第五十七条的规定，当事人有要求举行听证的权利。如你（单位）要求举行听证，应当在收到本告知书后5日内向本局提出申请。逾期未提出的，视为放弃要求举行听证的权利。</p>
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="content-section">
            <div class="contact-info">
              <span>本局地址：</span>
              <el-input
                v-model="formData.bureau_address"
                placeholder="机关地址"
                style="width: 300px;"
              />
              <span style="margin-left: 20px;">邮编：</span>
              <el-input
                v-model="formData.bureau_postal_code"
                placeholder="邮编"
                style="width: 120px;"
              />
              <span style="margin-left: 20px;">联系电话：</span>
              <el-input
                v-model="formData.bureau_contact_phone"
                placeholder="联系电话"
                style="width: 150px;"
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>落款（印章）</span>
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sign_year"
                placeholder="年"
                style="width: 80px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.sign_month"
                placeholder="月"
                style="width: 60px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.sign_day"
                placeholder="日"
                style="width: 60px;"
              />
              <span>日</span>
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据 - 使用下划线分割的字段名
const formData = ref({
  bureau_name: '惠阳区',
  document_prefix: '惠阳烟听告',
  document_year: '2024',
  document_serial: '5',
  involved_person: '',
  violation_year: '2024',
  violation_month: '1',
  violation_day: '15',
  case_nature: '',
  violated_law: '',
  legal_basis: '',
  administrative_penalty: '',
  bureau_address: '',
  bureau_postal_code: '',
  bureau_contact_phone: '',
  official_seal: '',
  sign_year: '2024',
  sign_month: '1',
  sign_day: '15'
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      bureau_name: docContent.bureau_name || newVal.bureau_name || '',
      document_prefix: docContent.document_prefix || newVal.document_prefix || '',
      document_year: docContent.document_year || newVal.document_year || '',
      document_serial: docContent.document_serial || newVal.document_serial || '',
      involved_person: docContent.involved_person || newVal.involved_person || '',
      violation_year: docContent.violation_year || newVal.violation_year || '',
      violation_month: docContent.violation_month || newVal.violation_month || '',
      violation_day: docContent.violation_day || newVal.violation_day || '',
      case_nature: docContent.case_nature || newVal.case_nature || '',
      violated_law: docContent.violated_law || newVal.violated_law || '',
      legal_basis: docContent.legal_basis || newVal.legal_basis || '',
      administrative_penalty: docContent.administrative_penalty || newVal.administrative_penalty || '',
      bureau_address: docContent.bureau_address || newVal.bureau_address || '',
      bureau_postal_code: docContent.bureau_postal_code || newVal.bureau_postal_code || '',
      bureau_contact_phone: docContent.bureau_contact_phone || newVal.bureau_contact_phone || '',
      sign_year: docContent.sign_year || newVal.sign_year || '',
      sign_month: docContent.sign_month || newVal.sign_month || '',
      sign_day: docContent.sign_day || newVal.sign_day || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    bureau_name: formData.value.bureau_name,
    document_prefix: formData.value.document_prefix,
    document_year: formData.value.document_year,
    document_serial: formData.value.document_serial,
    involved_person: formData.value.involved_person,
    violation_year: formData.value.violation_year,
    violation_month: formData.value.violation_month,
    violation_day: formData.value.violation_day,
    case_nature: formData.value.case_nature,
    violated_law: formData.value.violated_law,
    legal_basis: formData.value.legal_basis,
    administrative_penalty: formData.value.administrative_penalty,
    bureau_address: formData.value.bureau_address,
    bureau_postal_code: formData.value.bureau_postal_code,
    bureau_contact_phone: formData.value.bureau_contact_phone,
    sign_year: formData.value.sign_year,
    sign_month: formData.value.sign_month,
    sign_day: formData.value.sign_day
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '听证告知书'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'hearingInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
.hearing-notice-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.violation-info {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.hearing-rights-notice {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.hearing-rights-notice p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.contact-info span {
  white-space: nowrap;
}
</style>
