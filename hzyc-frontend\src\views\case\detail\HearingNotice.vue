<template>
  <div class="document-content">
    <div class="content-section">
      <div class="section-header">
        <div class="section-title">听证告知书</div>
        <el-button 
          size="small" 
          type="primary" 
          @click="toggleEdit"
        >
          {{ isEditing ? '保存' : '编辑' }}
        </el-button>
      </div>

      <!-- 文书标题 -->
      <div class="document-title">
        <div class="bureau-name">
          <template v-if="isEditing">
            <el-input v-model="formData.bureau_name" size="small" style="width: 200px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.bureau_name }}</span>
          </template>
          烟草专卖局
        </div>
        <h2>听证告知书</h2>
        <div class="document-number">
          <template v-if="isEditing">
            <el-input v-model="formData.document_prefix" size="small" style="width: 100px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.document_prefix }}</span>
          </template>
          （
          <template v-if="isEditing">
            <el-input v-model="formData.document_year" size="small" style="width: 80px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.document_year }}</span>
          </template>
          ）第
          <template v-if="isEditing">
            <el-input v-model="formData.document_serial" size="small" style="width: 80px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.document_serial }}</span>
          </template>
          号
        </div>
      </div>

      <!-- 收件人 -->
      <div class="recipient-section">
        <template v-if="isEditing">
          <el-input v-model="formData.involved_person" size="small" style="width: 200px;" />
        </template>
        <template v-else>
          <span class="underline-text">{{ formData.involved_person }}</span>
        </template>
        ：
      </div>

      <!-- 正文内容 -->
      <div class="document-body">
        <div class="content-paragraph">
          <span>经查，你（单位）于</span>
          <template v-if="isEditing">
            <el-input v-model="formData.violation_year" size="small" style="width: 80px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.violation_year }}</span>
          </template>
          <span>年</span>
          <template v-if="isEditing">
            <el-input v-model="formData.violation_month" size="small" style="width: 60px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.violation_month }}</span>
          </template>
          <span>月</span>
          <template v-if="isEditing">
            <el-input v-model="formData.violation_day" size="small" style="width: 60px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.violation_day }}</span>
          </template>
          <span>日因</span>
          <template v-if="isEditing">
            <el-input v-model="formData.case_nature" size="small" style="width: 300px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.case_nature }}</span>
          </template>
          <span>的行为，</span>
        </div>

        <div class="content-paragraph">
          <span>违反了</span>
          <template v-if="isEditing">
            <el-input v-model="formData.violated_law" size="small" style="width: 400px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.violated_law }}</span>
          </template>
          <span>的规定，依据</span>
          <template v-if="isEditing">
            <el-input v-model="formData.legal_basis" size="small" style="width: 300px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.legal_basis }}</span>
          </template>
          <span>，本局拟对</span>
        </div>

        <div class="content-paragraph">
          <span>你（单位）作出</span>
          <template v-if="isEditing">
            <el-input v-model="formData.administrative_penalty" size="small" style="width: 400px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.administrative_penalty }}</span>
          </template>
          <span>的行政处罚。</span>
        </div>

        <div class="content-paragraph">
          <span>根据《中华人民共和国行政处罚法》第六十三条、第六十四条和《烟草专卖行政处罚程序规定》第五十七条的规定，当事人有要求举行听证的权利。如你（单位）要求举行听证，应当在收到本告知书后5日内向本局提出申请。逾期未提出的，视为放弃要求举行听证的权利。</span>
        </div>

        <div class="contact-info">
          <span>本局地址：</span>
          <template v-if="isEditing">
            <el-input v-model="formData.bureau_address" size="small" style="width: 300px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.bureau_address }}</span>
          </template>
          <span style="margin-left: 50px;">邮编：</span>
          <template v-if="isEditing">
            <el-input v-model="formData.bureau_postal_code" size="small" style="width: 120px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.bureau_postal_code }}</span>
          </template>
          <span style="margin-left: 50px;">联系电话：</span>
          <template v-if="isEditing">
            <el-input v-model="formData.bureau_contact_phone" size="small" style="width: 150px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.bureau_contact_phone }}</span>
          </template>
        </div>
      </div>

      <!-- 签名区域 -->
      <div class="signature-section">
        <div class="signature-line">
          <span class="signature-label">签章（印章）：</span>
          <template v-if="isEditing">
            <el-input v-model="formData.official_seal" size="small" style="width: 150px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.official_seal }}</span>
          </template>
        </div>
        <div class="date-line">
          <template v-if="isEditing">
            <el-input v-model="formData.sign_year" size="small" style="width: 80px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.sign_year }}</span>
          </template>
          <span>年</span>
          <template v-if="isEditing">
            <el-input v-model="formData.sign_month" size="small" style="width: 60px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.sign_month }}</span>
          </template>
          <span>月</span>
          <template v-if="isEditing">
            <el-input v-model="formData.sign_day" size="small" style="width: 60px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.sign_day }}</span>
          </template>
          <span>日</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据 - 使用下划线分割的字段名
const formData = ref({
  bureau_name: '惠阳区',
  document_prefix: '惠阳烟听告',
  document_year: '2024',
  document_serial: '5',
  involved_person: '',
  violation_year: '2024',
  violation_month: '1',
  violation_day: '15',
  case_nature: '',
  violated_law: '',
  legal_basis: '',
  administrative_penalty: '',
  bureau_address: '',
  bureau_postal_code: '',
  bureau_contact_phone: '',
  official_seal: '',
  sign_year: '2024',
  sign_month: '1',
  sign_day: '15'
})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    formData.value = { ...formData.value, ...newVal }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  if (!props.documentData || Object.keys(props.documentData).length === 0) {
    // 可以在这里调用API获取真实数据
  }
})

// 切换编辑状态
const toggleEdit = () => {
  if (isEditing.value) {
    emit('save', formData.value)
    ElMessage.success('保存成功')
  }
  isEditing.value = !isEditing.value
}
</script>

<style scoped>
.document-content {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  background: white;
  line-height: 2;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.document-title {
  text-align: center;
  margin-bottom: 40px;
}

.bureau-name {
  font-size: 16px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.document-title h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0;
}

.document-number {
  font-size: 16px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.recipient-section {
  margin-bottom: 30px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.document-body {
  margin-bottom: 60px;
  font-size: 16px;
}

.content-paragraph {
  margin-bottom: 20px;
  text-indent: 2em;
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}

.contact-info {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}

.signature-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 20px;
  margin-top: 60px;
}

.signature-line {
  display: flex;
  align-items: center;
  gap: 10px;
}

.signature-label {
  font-weight: bold;
}

.date-line {
  display: flex;
  align-items: center;
  gap: 5px;
}

.underline-text {
  border-bottom: 1px solid #333;
  min-width: 60px;
  padding: 2px 5px;
  margin: 0 3px;
  display: inline-block;
  text-align: center;
}

/* 打印样式 */
@media print {
  .section-header {
    display: none;
  }
  
  .document-content {
    padding: 0;
    box-shadow: none;
  }
}
</style>
